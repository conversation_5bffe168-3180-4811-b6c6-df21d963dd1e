import React, { useRef, useState, useEffect, useCallback } from 'react';
import './Homepage.css';

/**
 * Interface untuk data produk carousel
 * Mendefinisikan struktur data untuk setiap item produk
 */
interface ProductItem {
  id: number;
  image: string;
  title: string;
  topic: string;
  description: string;
  detailTitle: string;
  detailDescription: string;
  specifications: {
    pressure: string;
    refrigerant: string;
    compatibility: string;
    capacity: string;
    control: string;
  };
}

/**
 * Interface untuk data mitra bisnis
 */
interface BusinessPartner {
  id: number;
  name: string;
  period: string;
  initials: string;
  gradient: string;
}


/**
 * Komponen Homepage dengan carousel produk
 * Menampilkan slider produk dengan animasi dan detail produk
 */
const Homepage: React.FC = () => {
  // State untuk mengontrol carousel
  const [isShowingDetail, setIsShowingDetail] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentProductIndex, setCurrentProductIndex] = useState(0);

  // State untuk date/time
  const [currentDateTime, setCurrentDateTime] = useState(new Date());

  // State untuk weather info
  const [weatherInfo] = useState({
    temperature: '33°',
    condition: 'Cerah',
    icon: '☀️'
  });

  // Refs untuk manipulasi DOM - separate refs for each layout
  const carouselRefDesktop = useRef<HTMLDivElement>(null);
  const listRefDesktop = useRef<HTMLDivElement>(null);
  const carouselRefTablet = useRef<HTMLDivElement>(null);
  const listRefTablet = useRef<HTMLDivElement>(null);
  const carouselRefMobile = useRef<HTMLDivElement>(null);
  const listRefMobile = useRef<HTMLDivElement>(null);

  // Auto-rotation timer ref
  const autoRotationTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Update date/time setiap detik
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Helper functions untuk format date/time
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Data produk untuk carousel
  const products: ProductItem[] = [
    {
      id: 1,
      image: '/images/product/img1.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kompresor handal untuk menjaga kabin alat berat tetap sejuk',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Didesain khusus untuk alat berat tambang yang bekerja di bawah suhu tinggi dan debu tebal. Menghasilkan sirkulasi udara dingin stabil, menjaga kenyamanan operator agar tetap fokus sepanjang shift.',
      specifications: {
        pressure: '15.000 psi',
        refrigerant: 'R-134a',
        compatibility: 'Excavator, Loader',
        capacity: '10-14k BTU',
        control: 'Manual Kabin'
      }
    },
    {
      id: 2,
      image: '/images/product/img2.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL B',
      description: 'Performa bertenaga untuk sistem pendingin AC alat berat',
      detailTitle: 'COMPRESSOR AC HD MODEL B',
      detailDescription: 'Menggunakan komponen internal presisi yang mampu beroperasi pada tekanan tinggi. Sangat cocok untuk alat berat yang bekerja di tambang terbuka dengan jam kerja panjang.',
      specifications: {
        pressure: '18.000 psi',
        refrigerant: 'R-134a/R-404a',
        compatibility: 'Dump Truck',
        capacity: '12-16k BTU',
        control: 'Semi Otomatis'
      }
    },
    {
      id: 3,
      image: '/images/product/img3.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL C',
      description: 'Efisiensi dan daya tahan tinggi untuk suhu kabin yang selalu optimal.',
      detailTitle: 'COMPRESSOR AC HD MODEL C',
      detailDescription: 'Dilengkapi sistem pendinginan cepat yang hemat energi. Ideal untuk unit alat berat yang bekerja di daerah dengan perbedaan suhu ekstrem siang dan malam.',
      specifications: {
        pressure: '20.000 psi',
        refrigerant: 'R-404a',
        compatibility: 'Multi Brand',
        capacity: '14-18k BTU',
        control: 'Sensor Suhu'
      }
    },
    {
      id: 4,
      image: '/images/product/img4.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL D',
      description: 'Kapasitas maksimal untuk menjaga kabin tetap dingin selama 24 jam kerja nonstop.',
      detailTitle: 'COMPRESSOR AC HD MODEL D',
      detailDescription: 'Dirancang dengan material heavy-duty yang mampu bertahan dari getaran keras, tekanan tinggi, dan cuaca ekstrem di area tambang. Memberikan aliran udara dingin konsisten demi produktivitas optimal.',
      specifications: {
        pressure: '25.000 psi',
        refrigerant: 'R-404a',
        compatibility: 'Heavy Loader',
        capacity: '16-20k BTU',
        control: 'Auto Kabin'
      }
    }
  ]

  // Data Mitra Bisnis - 5 Contoh
  const businessPartners: BusinessPartner[] = [
    {
      id: 1,
      name: 'PT. UNGGUL DINAMIKA UTAMA',
      period: 'SEP 2016 - SEKARANG',
      initials: 'UDU',
      gradient: 'from-blue-500 to-purple-600',
    },
    {
      id: 2,
      name: 'PT. INDO MURO KENCANA',
      period: 'OKT 2017 - SEKARANG',
      initials: 'IMK',
      gradient: 'from-green-500 to-teal-600',
    },
    {
      id: 3,
      name: 'PT. PUTRA PERKASA ABADI',
      period: 'JAN 2018 - SEKARANG',
      initials: 'PPA',
      gradient: 'from-red-400 to-pink-500',
    },
    {
      id: 4,
      name: 'PT. ADARO ENERGY',
      period: 'JUN 2020 - SEKARANG',
      initials: 'ADRO',
      gradient: 'from-cyan-500 to-blue-500',
    },
  ];

  /**
   * Fungsi untuk menggeser carousel ke slide berikutnya atau sebelumnya
   * @param direction - 'next' untuk slide berikutnya, 'prev' untuk slide sebelumnya
   * @param isAutoRotation - apakah ini dipanggil dari auto-rotation
   */
  const showSlider = useCallback((direction: 'next' | 'prev', isAutoRotation: boolean = false) => {
    if (isAnimating || isShowingDetail) return;

    if (!isAutoRotation && autoRotationTimerRef.current) {
      clearTimeout(autoRotationTimerRef.current);
      startAutoRotation();
    }

    setIsAnimating(true);

    const carouselRefs = [carouselRefDesktop, carouselRefTablet, carouselRefMobile];
    const listRefs = [listRefDesktop, listRefTablet, listRefMobile];

    carouselRefs.forEach((carouselRef, index) => {
      const listRef = listRefs[index];
      if (!listRef.current || !carouselRef.current) return;

      const items = listRef.current.querySelectorAll('.carousel-item');
      const carousel = carouselRef.current;

      carousel.classList.remove('next', 'prev');

      if (direction === 'next') {
        if (items[0]) {
          listRef.current.appendChild(items[0]);
        }
        carousel.classList.add('next');
        setCurrentProductIndex(prev => (prev + 1) % products.length);
      } else {
        if (items[items.length - 1]) {
          listRef.current.prepend(items[items.length - 1]);
        }
        carousel.classList.add('prev');
        setCurrentProductIndex(prev => (prev - 1 + products.length) % products.length);
      }
    });

    setTimeout(() => {
      setIsAnimating(false);
    }, 2000);
  }, [isAnimating, isShowingDetail, products.length]);

  /**
   * Fungsi untuk memulai auto-rotation carousel
   */
  const startAutoRotation = useCallback(() => {
    if (autoRotationTimerRef.current) {
      clearTimeout(autoRotationTimerRef.current);
    }

    autoRotationTimerRef.current = setTimeout(() => {
      if (!isShowingDetail && !isAnimating) {
        showSlider('next', true);
      }
      startAutoRotation();
    }, 10000);
  }, [isShowingDetail, isAnimating, showSlider]);

  /**
   * Fungsi untuk menghentikan auto-rotation
   */
  const stopAutoRotation = useCallback(() => {
    if (autoRotationTimerRef.current) {
      clearTimeout(autoRotationTimerRef.current);
      autoRotationTimerRef.current = null;
    }
  }, []);

  useEffect(() => {
    startAutoRotation();
    return () => {
      stopAutoRotation();
    };
  }, [startAutoRotation, stopAutoRotation]);

  /**
   * Fungsi untuk menampilkan detail produk
   */
  const showDetail = useCallback(() => {
    stopAutoRotation();
    const carouselRefs = [carouselRefDesktop, carouselRefTablet, carouselRefMobile];
    carouselRefs.forEach((carouselRef) => {
      if (!carouselRef.current) return;
      carouselRef.current.classList.remove('next', 'prev');
      carouselRef.current.classList.add('showDetail');
    });
    setIsShowingDetail(true);
  }, [stopAutoRotation]);

  /**
   * Fungsi untuk kembali ke tampilan carousel
   */
  const hideDetail = useCallback(() => {
    const carouselRefs = [carouselRefDesktop, carouselRefTablet, carouselRefMobile];
    carouselRefs.forEach((carouselRef) => {
      if (!carouselRef.current) return;
      carouselRef.current.classList.remove('showDetail');
    });
    setIsShowingDetail(false);
    startAutoRotation();
  }, [startAutoRotation]);

  const PartnerCard = ({ partner }: { partner: BusinessPartner }) => (
    <div className="business-info-card flex items-center gap-3 bg-white/80 rounded-lg p-3 shadow-sm">
      <div className={`w-8 h-8 bg-gradient-to-r ${partner.gradient} rounded-md flex items-center justify-center`}>
        <span className="text-white text-xs font-bold">{partner.initials}</span>
      </div>
      <div>
        <div className="text-sm font-semibold text-gray-800">{partner.name}</div>
        <div className="text-xs text-gray-600">{partner.period}</div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen professional-gradient-bg text-base-content overflow-hidden">
      {/* Header dengan logo perusahaan */}
      <header className="w-full max-w-7xl mx-auto px-4 py-3 flex items-center justify-start">
        <div className="flex items-center gap-4">
          <img
            src="/images/icons/icon-192x192.png"
            alt="PT Putera Wibowo Borneo Logo"
            className="w-10 h-10 object-contain"
          />
          <h3 className="font-bold text-primary">
            PT Putera Wibowo Borneo
          </h3>
        </div>
      </header>

      {/* Main Content Area - Responsive Grid Layout */}
      <div className="flex-1 h-[calc(100vh-80px)] relative">

        {/* Tablet Landscape Layout (1024px+) */}
        <div className="hidden lg:grid lg:grid-cols-[35%_65%] h-full">
          {/* Left Section */}
          <div className="flex flex-col justify-between bg-gradient-to-br from-primary/10 to-secondary/10 text-base-content relative overflow-hidden p-8">
            {/* Top Section - Weather */}
            <div>
              <div className="flex items-center gap-3 mb-2">
                <span className="text-4xl">{weatherInfo.icon}</span>
                <div>
                  <div className="text-2xl font-bold text-primary">{weatherInfo.temperature}</div>
                  <div className="text-lg text-base-content/80">{weatherInfo.condition}</div>
                </div>
              </div>
            </div>

            {/* Middle Section - Time & Date */}
            <div className="text-left py-2 flex-grow flex flex-col justify-left">
              <div className="datetime-time text-primary text-4 xl font-bold leading-tight">
                {formatTime(currentDateTime)}
              </div>
              <div className="mt-2 font-small">
                {formatDate(currentDateTime)}
              </div>
              <div className="text-primary/70 italic mt-1 mb-6">
                Hari pendidikan nasional
              </div>
            </div>

            {/* Bottom Section - Business & Stats */}
            <div className="flex justify-between items-center">
              {/* Business Info */}
              <div className="w-3/4 mr-4">
                <div className="text-left">
                  <div className="text-xl font-semi text-primary mb-3">Mitra Bisnis Kami</div>
                  <div className="partner-scroll-container">
                    <div className="partner-scroll-list">
                      {[...businessPartners, ...businessPartners].map((partner, index) => (
                        <PartnerCard key={`${partner.id}-${index}`} partner={partner} />
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div>
                <div className="mb-4 stats-card bg-white/80 rounded-lg p-4 text-center shadow-sm min-w-[80px]">
                  <div className="text-2xl font-bold text-primary mb-1">20</div>
                  <div className="text-xs text-gray-600 leading-tight">utang invoice</div>
                </div>
                <div className="stats-card bg-white/80 rounded-lg p-4 text-center shadow-sm min-w-[80px]">
                  <div className="text-2xl font-bold text-primary mb-1">40</div>
                  <div className="text-xs text-gray-600 leading-tight">Pending PO</div>
                </div>
              </div>
            </div>


            {/* Product Index Indicator */}
            <div className="absolute top-1/2 left-4 transform -translate-y-1/2 z-10">
              <div className="text-sm text-base-content/60 font-medium">
                {String(currentProductIndex + 1).padStart(2, '0')} / {String(products.length).padStart(2, '0')}
              </div>
            </div>

            {/* Decorative background elements */}
            <div className="absolute top-10 left-10 w-32 h-32 bg-primary/10 rounded-full blur-xl"></div>
            <div className="absolute bottom-10 right-10 w-24 h-24 bg-secondary/10 rounded-full blur-lg"></div>
          </div>

          {/* Right Section - Product Carousel (65% width) */}
          <div className="relative bg-base-100">
            {/* Product Section Content */}
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRefDesktop} className="carousel h-full max-h-[550px] py-4">
                <div ref={listRefDesktop} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70 max-w-1/2">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>LIHAT DETAIL &#8599;</button>
                      </div>
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>
                        <div className="specifications">
                          {Object.entries(product.specifications).map(([key, value]) => (
                            <div key={key} className="spec-item">
                              <p className="text-base-content font-semibold">{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</p>
                              <p className="text-base-content/70">{value}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {/* Tombol navigasi -- MODIFIED */}
                <div className="arrows">
                  <button id="prev" className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content" onClick={() => showSlider('prev')} disabled={isAnimating}>&#8249;</button>
                  <button id="next" className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content" onClick={() => showSlider('next')} disabled={isAnimating}>&#8250;</button>
                  <button id="back" className={`back-btn mt-10 text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`} onClick={hideDetail}>Kembali &#8599;</button>
                </div>

                {/* Auto-rotation indicator */}
                {!isShowingDetail && (
                  <div className="auto-rotation-indicator">
                    <div className="progress-bar"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* abikan ini dulu =================================================================================== */}
        {/* Tablet Portrait Layout (768px - 1023px) */}
        <div className="hidden md:flex lg:hidden flex-col h-full">
          {/* Top Section - Date/Time (40% height) */}
          <div className="h-2/5 flex flex-col items-center justify-center bg-gradient-to-br from-primary/20 to-secondary/20 text-base-content relative overflow-hidden">
            {/* Weather info - top right */}
            <div className="absolute top-4 right-4 text-right z-10">
              <div className="flex items-center gap-2">
                <span className="text-2xl">{weatherInfo.icon}</span>
                <div>
                  <div className="text-xl font-bold text-primary">{weatherInfo.temperature}</div>
                  <div className="text-sm text-base-content/80">{weatherInfo.condition}</div>
                </div>
              </div>
            </div>

            {/* Main content */}
            <div className="text-center z-10 p-6 datetime-display max-w-full">
              <div className="text-5xl font-bold text-primary mb-3 datetime-time tracking-wider">
                {formatTime(currentDateTime)}
              </div>
              <div className="text-lg font-medium text-base-content/80 leading-relaxed datetime-date mb-2">
                {formatDate(currentDateTime)}
              </div>
              <div className="text-sm text-primary/70 font-medium italic">
                Hari pendidikan nasional
              </div>
            </div>

            {/* Product index - left side */}
            <div className="absolute top-1/2 left-4 transform -translate-y-1/2 z-10">
              <div className="text-xs text-base-content/60 font-medium">
                {String(currentProductIndex + 1).padStart(2, '0')} / {String(products.length).padStart(2, '0')}
              </div>
            </div>

            {/* Decorative background elements */}
            <div className="absolute top-5 left-5 w-20 h-20 bg-primary/10 rounded-full blur-lg"></div>
            <div className="absolute bottom-5 right-5 w-16 h-16 bg-secondary/10 rounded-full blur-md"></div>
          </div>

          {/* Bottom Section - Product Carousel (60% height) */}
          <div className="h-3/5 relative bg-base-100">
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRefTablet} className="carousel h-full">
                <div ref={listRefTablet} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />

                      {/* Konten perkenalan produk */}
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>
                          Lihat Detail &#8599;
                        </button>
                      </div>

                      {/* Detail produk */}
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>

                        {/* Spesifikasi produk */}
                        <div className="specifications">
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Used Time</p>
                            <p className="text-base-content/70">{product.specifications.pressure}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Charging Port</p>
                            <p className="text-base-content/70">{product.specifications.refrigerant}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Compatible</p>
                            <p className="text-base-content/70">{product.specifications.compatibility}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Bluetooth</p>
                            <p className="text-base-content/70">{product.specifications.capacity}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Controlled</p>
                            <p className="text-base-content/70">{product.specifications.control}</p>
                          </div>
                        </div>

                        {/* Tombol checkout */}
                        <div className="checkout">
                          <button className="btn btn-outline">ADD TO CART</button>
                          <button className="btn btn-primary">CHECKOUT</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Tombol navigasi */}
                <div className="arrows">
                  <button
                    id="prev"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('prev')}
                    disabled={isAnimating}
                  >
                    &#8249;
                  </button>
                  <button
                    id="next"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('next')}
                    disabled={isAnimating}
                  >
                    &#8250;
                  </button>
                  <button
                    id="back"
                    className={`back-btn text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`}
                    onClick={hideDetail}
                  >
                    See All &#8599;
                  </button>
                </div>

                {/* Auto-rotation indicator */}
                {!isShowingDetail && (
                  <div className="auto-rotation-indicator">
                    <div className="progress-bar"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Layout (< 768px) */}
        <div className="md:hidden h-full">
          {/* Mobile uses existing carousel layout with improved styling */}
          <div className="h-full relative bg-base-100">
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRefMobile} className="carousel h-fit">
                <div ref={listRefMobile} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />

                      {/* Konten perkenalan produk */}
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>
                          LIHAT DETAIL &#8599;
                        </button>
                      </div>

                      {/* Detail produk */}
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>

                        {/* Spesifikasi produk */}
                        <div className="specifications">
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Used Time</p>
                            <p className="text-base-content/70">{product.specifications.pressure}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Charging Port</p>
                            <p className="text-base-content/70">{product.specifications.refrigerant}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Compatible</p>
                            <p className="text-base-content/70">{product.specifications.compatibility}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Bluetooth</p>
                            <p className="text-base-content/70">{product.specifications.capacity}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Controlled</p>
                            <p className="text-base-content/70">{product.specifications.control}</p>
                          </div>
                        </div>

                        {/* Tombol checkout */}
                        <div className="checkout">
                          <button className="btn btn-outline">ADD TO CART</button>
                          <button className="btn btn-primary">CHECKOUT</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Tombol navigasi */}
                <div className="arrows">
                  <button
                    id="prev"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('prev')}
                    disabled={isAnimating}
                  >
                    &#8249;
                  </button>
                  <button
                    id="next"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('next')}
                    disabled={isAnimating}
                  >
                    &#8250;
                  </button>
                  <button
                    id="back"
                    className={`back-btn text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`}
                    onClick={hideDetail}>
                    See All &#8599;
                  </button>
                </div>

                {/* Auto-rotation indicator */}
                {!isShowingDetail && (
                  <div className="auto-rotation-indicator">
                    <div className="progress-bar"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Homepage;