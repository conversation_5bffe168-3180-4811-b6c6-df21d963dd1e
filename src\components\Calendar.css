/* Calendar Component Styles - Responsive untuk semua ukuran layar */

/* Consistent Gradient Background System - matching Homepage */
.gradient-bg-primary {
  background: linear-gradient(135deg, oklch(var(--p) / 0.15) 0%, oklch(var(--s) / 0.1) 50%, oklch(var(--a) / 0.05) 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(45deg, oklch(var(--s) / 0.1) 0%, oklch(var(--p) / 0.08) 50%, oklch(var(--a) / 0.12) 100%);
}

.gradient-bg-accent {
  background: linear-gradient(225deg, oklch(var(--a) / 0.1) 0%, oklch(var(--p) / 0.05) 50%, oklch(var(--s) / 0.08) 100%);
}

/* Main container */
.calendar-container {
  width: 100%;
  height: calc(100vh - 80px); /* Account for header */
  display: flex;
  gap: 20px;
  padding: 20px;
  font-family: "Poppins", sans-serif;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  /* Android/Web font consistency */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
  color: oklch(var(--bc));
}

/* Ensure all child elements use border-box and consistent fonts */
.calendar-container *,
.calendar-container *::before,
.calendar-container *::after {
  box-sizing: border-box;
  font-family: inherit;
  color: inherit;
}

/* Android-specific font fixes for Calendar */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  .calendar-container {
    font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif !important;
  }

  /* Ensure proper color rendering on Android WebView */
  .calendar-container * {
    -webkit-text-fill-color: inherit !important;
  }

  /* Ensure proper font weight rendering on Android */
  .font-semibold,
  .calendar-title {
    font-weight: 600 !important;
  }

  .font-bold {
    font-weight: 700 !important;
  }

  .font-medium {
    font-weight: 500 !important;
  }
}

/* Main Calendar Section (70%) */
.main-calendar-section {
  flex: 0 0 70%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
}

/* Calendar Header */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.calendar-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--fallback-p, oklch(var(--p)));
  margin: 0;
}

.nav-button {
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nav-button:hover {
  background: var(--fallback-s, oklch(var(--s)));
  transform: scale(1.1);
}

/* Calendar Grid */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  flex: 1;
  background: var(--fallback-b3, oklch(var(--b3)));
  border-radius: 12px;
  padding: 10px;
  overflow: hidden;
}

.day-header {
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  padding: 10px;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  border-radius: 6px;
}

.calendar-date {
  background: var(--fallback-b1, oklch(var(--b1)));
  padding: 8px 4px;
  cursor: pointer;
  border-radius: 6px;
  position: relative;
  transition: all 0.2s ease;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  font-weight: 500;
  overflow: hidden;
}

.calendar-date:hover {
  background: var(--fallback-b2, oklch(var(--b2)));
  transform: scale(1.02);
}

.calendar-date.other-month {
  color: var(--fallback-bc, oklch(var(--bc) / 0.4));
  background: var(--fallback-b1, oklch(var(--b1) / 0.5));
}

.calendar-date.today {
  background: var(--fallback-a, oklch(var(--a)));
  color: var(--fallback-ac, oklch(var(--ac)));
  font-weight: 700;
  box-shadow: 0 0 0 2px var(--fallback-p, oklch(var(--p)));
}

.calendar-date.has-tasks {
  background: var(--fallback-s, oklch(var(--s) / 0.2));
  border: 2px solid var(--fallback-s, oklch(var(--s)));
}

/* Date number styling */
.date-number {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--fallback-bc, oklch(var(--bc)));
  margin-bottom: 2px;
  align-self: center;
}

/* Task display in calendar dates */
.date-tasks {
  display: flex;
  flex-direction: column;
  gap: 1px;
  width: 100%;
  flex: 1;
  overflow: hidden;
}

.task-chip {
  background: var(--fallback-p, oklch(var(--p) / 0.1));
  border-left: 2px solid var(--fallback-p, oklch(var(--p)));
  padding: 1px 3px;
  border-radius: 2px;
  font-size: 0.6rem;
  line-height: 1.1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.task-time {
  font-weight: 600;
  color: var(--fallback-p, oklch(var(--p)));
  font-size: 0.55rem;
}

.task-name {
  color: var(--fallback-bc, oklch(var(--bc) / 0.8));
  font-size: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-tasks {
  background: var(--fallback-s, oklch(var(--s) / 0.1));
  border-left-color: var(--fallback-s, oklch(var(--s)));
  color: var(--fallback-s, oklch(var(--s)));
  font-weight: 600;
  text-align: center;
  justify-content: center;
  align-items: center;
  display: flex;
  font-size: 0.5rem;
}

/* Task Summary Section */
.task-summary-section {
  flex: 0 0 80px;
  display: flex;
  align-items: right;
  justify-content: right;
}

.task-stats {
  display: flex;
  gap: 20px;
  width: 100%;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--fallback-p, oklch(var(--p)));
  line-height: 1;
}

.stat-label {
  font-size: 0.7rem;
  color: var(--fallback-bc, oklch(var(--bc) / 0.7));
  font-weight: 500;
  margin-top: 2px;
}

.task-info h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--fallback-bc, oklch(var(--bc)));
}

.task-info p {
  margin: 0;
  font-size: 0.85rem;
  color: var(--fallback-bc, oklch(var(--bc) / 0.7));
}

.task-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-status {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
}

.task-status.pending {
  background: var(--fallback-w, oklch(var(--w)));
  color: var(--fallback-wc, oklch(var(--wc)));
}

.task-status.notified {
  background: var(--fallback-su, oklch(var(--su)));
  color: var(--fallback-suc, oklch(var(--suc)));
}

.delete-task-btn {
  background: var(--fallback-er, oklch(var(--er)));
  color: var(--fallback-erc, oklch(var(--erc)));
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.delete-task-btn:hover {
  background: var(--fallback-er, oklch(var(--er) / 0.8));
  transform: scale(1.1);
}

.delete-task-btn:active {
  transform: scale(0.95);
}

/* Secondary Section (30%) */
.secondary-section {
  flex: 0 0 30%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
}

/* Secondary Calendar */
.secondary-calendar {
  background: var(--fallback-b2, oklch(var(--b2)));
  padding: 15px;
  border-radius: 12px;
}

.secondary-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--fallback-p, oklch(var(--p)));
  margin: 0 0 15px 0;
  text-align: center;
}

.secondary-calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
}

.secondary-day-header {
  background: var(--fallback-p, oklch(var(--p) / 0.8));
  color: var(--fallback-pc, oklch(var(--pc)));
  padding: 5px;
  text-align: center;
  font-weight: 600;
  font-size: 0.7rem;
  border-radius: 4px;
}

.secondary-date {
  background: var(--fallback-b1, oklch(var(--b1)));
  padding: 8px 4px;
  text-align: center;
  border-radius: 4px;
  font-size: 0.8rem;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.secondary-date:hover {
  background: var(--fallback-b3, oklch(var(--b3)));
}

.secondary-date.other-month {
  color: var(--fallback-bc, oklch(var(--bc) / 0.4));
  background: var(--fallback-b1, oklch(var(--b1) / 0.5));
}

.secondary-date.today {
  background: var(--fallback-a, oklch(var(--a)));
  color: var(--fallback-ac, oklch(var(--ac)));
  font-weight: 700;
}

.secondary-date.has-tasks {
  background: var(--fallback-s, oklch(var(--s) / 0.3));
  border: 1px solid var(--fallback-s, oklch(var(--s)));
}

/* Task Form Section */
.task-form-section {
  background: var(--fallback-b2, oklch(var(--b2)));
  padding: 2px 20px;
  border-radius: 12px;
}

.task-form-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--fallback-p, oklch(var(--p)));
  margin: 0 0 10px 0;
}

.task-form {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.form-group {
  justify-content: space-between;
  display: flex;
  gap: 1px;
}
.form-group input[type="date"] {
  flex: 1;
}
.form-group input[type="text"] {
  flex: 1;
}

.form-group label {
  font-weight: 500;
  color: var(--fallback-bc, oklch(var(--bc)));
  font-size: 0.9rem;
}

.form-group input {
  padding: 10px;
  border: 0.5px solid #8881e975;
  box-shadow: 1px 2px 3px -6px rgba(0, 0, 0, 0.7);
  -webkit-box-shadow: 1px 6px 3px -6px rgba(0, 0, 0, 0.7);
  -moz-box-shadow: 1px 6px 3px -6px rgba(0, 0, 0, 0.7);
  border-radius: 6px;
  background: var(--fallback-b1, oklch(var(--b1)));
  color: var(--fallback-bc, oklch(var(--bc)));
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--fallback-p, oklch(var(--p)));
  box-shadow: 0 0 0 2px var(--fallback-p, oklch(var(--p) / 0.2));
}

.submit-button {
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  border: 1px solid #8881e993;
  padding: 12px 20px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.submit-button:hover {
  background: var(--fallback-s, oklch(var(--s)));
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Floating Action Button */
.fab-home {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  border: none;
  border-radius: 50%;
  font-weight: 700;
  font-size: 0.8rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
}

.fab-home:hover {
  background: var(--fallback-s, oklch(var(--s)));
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.fab-home:active {
  transform: scale(0.95);
}

/* Tablet Landscape Specific (1024px - 1366px) */
@media screen and (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
  .calendar-container {
    height: calc(100vh - 80px);
    overflow: hidden;
    gap: 15px;
    padding: 15px;
  }

  .main-calendar-section {
    flex: 0 0 65%; /* Optimize space usage */
  }

  .secondary-section {
    flex: 0 0 35%;
    flex-direction: column;
    gap: 10px;
  }

  .calendar-title {
    font-size: 1.8rem;
  }

  .calendar-date {
    min-height: 45px;
    padding: 8px 5px;
    font-size: 0.9rem;
  }

  .day-header {
    padding: 8px;
    font-size: 0.85rem;
  }

  .task-summary-section {
    flex: 0 0 60px;
  }

  .stat-number {
    font-size: 1.2rem;
  }

  .stat-label {
    font-size: 0.65rem;
  }

  .secondary-calendar {
    flex: 1;
    height: 100%;
  }

  .task-form-section {
    flex: 0 0 auto;
  }

  /* Ensure form doesn't require scrolling */
  .task-form {
    gap: 8px;
  }

  .form-group {
    margin-bottom: 8px;
  }

  .form-input {
    padding: 8px;
    font-size: 0.9rem;
  }

  .form-button {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

/* Responsive Design untuk Tablet Portrait */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .calendar-container {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    height: calc(100vh - 80px);
    overflow: hidden;
  }

  .main-calendar-section {
    flex: 1;
    min-height: 0;
  }

  .secondary-section {
    flex: 0 0 auto;
    flex-direction: row;
    gap: 15px;
    max-height: 200px;
  }

  .secondary-calendar {
    flex: 1;
  }

  .task-form-section {
    flex: 1;
  }

  .calendar-title {
    font-size: 1.5rem;
  }

  .calendar-date {
    min-height: 50px;
    padding: 10px 5px;
  }

  .task-summary-section {
    flex: 0 0 60px;
  }
}

/* Responsive Design untuk Mobile */
@media screen and (max-width: 768px) {
  .calendar-container {
    padding: 10px;
    gap: 10px;
    height: 100vh;
    overflow-y: auto;
  }

  .secondary-section {
    flex-direction: column;
  }

  .calendar-title {
    font-size: 1.2rem;
  }

  .nav-button {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .calendar-date {
    min-height: 40px;
    padding: 8px 4px;
    font-size: 0.9rem;
  }

  .day-header {
    padding: 8px;
    font-size: 0.8rem;
  }

  .task-summary-section {
    flex: 0 0 50px;
  }

  .stat-number {
    font-size: 1rem;
  }

  .stat-label {
    font-size: 0.6rem;
  }

  .fab-home {
    width: 50px;
    height: 50px;
    bottom: 20px;
    right: 20px;
    font-size: 0.7rem;
  }

  .secondary-calendar-grid {
    gap: 1px;
  }

  .secondary-date {
    min-height: 25px;
    padding: 5px 2px;
    font-size: 0.7rem;
  }

  .secondary-day-header {
    padding: 3px;
    font-size: 0.6rem;
  }

  /* Improve form layout on mobile */
  .task-form-section {
    padding: 15px;
  }

  .form-group input {
    padding: 12px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .submit-button {
    padding: 12px 20px;
    font-size: 16px;
  }
}

/* Extra small screens */
@media screen and (max-width: 480px) {
  .calendar-container {
    padding: 5px;
  }

  .calendar-grid {
    padding: 5px;
    gap: 1px;
  }

  .calendar-date {
    min-height: 35px;
    padding: 5px 2px;
    font-size: 0.8rem;
  }

  .day-header {
    padding: 5px;
    font-size: 0.7rem;
  }

  .calendar-title {
    font-size: 1rem;
  }

  .nav-button {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .task-form-section {
    padding: 15px;
  }

  .form-group input {
    padding: 8px;
    font-size: 0.8rem;
  }

  .submit-button {
    padding: 10px 15px;
    font-size: 0.8rem;
  }
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .calendar-container {
    flex-direction: row;
    height: 100vh;
    overflow: hidden;
  }

  .main-calendar-section {
    flex: 0 0 65%;
  }

  .secondary-section {
    flex: 0 0 35%;
    flex-direction: column;
  }

  .task-summary-section {
    flex: 0 0 50px;
  }

  .calendar-date {
    min-height: 30px;
    padding: 5px;
    font-size: 0.8rem;
  }

  .day-header {
    padding: 5px;
    font-size: 0.7rem;
  }
}
