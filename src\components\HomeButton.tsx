import React from 'react';

/**
 * Props for HomeButton component
 */
interface HomeButtonProps {
  className?: string;
  onClick?: () => void;
}

/**
 * Universal Home Button Component
 * Positioned in bottom-right corner for all non-home pages
 * Consistent styling across all components
 */
const HomeButton: React.FC<HomeButtonProps> = ({ className = '', onClick }) => {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Default navigation behavior
      window.dispatchEvent(new CustomEvent('navigateToHome'));
    }
  };

  return (
    <div className={`fixed bottom-3 right-3 z-50 ${className}`}>
      <button
        onClick={handleClick}
        className="btn btn-primary btn-circle btn-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
        aria-label="Kembali ke Home"
      >
        <span className="text-xl">🏠</span>
      </button>
    </div>
  );
};

export default HomeButton;
