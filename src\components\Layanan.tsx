import React, { useEffect, useState } from 'react';
import './Layanan.css';
import type { LayananItem } from '../types';
import UniversalHeader from './UniversalHeader';
import HomeButton from './HomeButton';

/**
 * Komponen Layanan - Menampilkan layanan perusahaan
 * Layout berdasarkan gambar referensi dengan 3 kartu layanan
 * Optimized for identical rendering in web and Capacitor PWA
 */
const Layanan: React.FC = () => {
  // State untuk mendeteksi platform (web vs Capacitor)
  const [isCapacitor, setIsCapacitor] = useState(false);

  // Deteksi platform saat komponen dimount
  useEffect(() => {
    const detectPlatform = () => {
      // Deteksi Capacitor
      const capacitorDetected = !!(window as any).Capacitor;
      setIsCapacitor(capacitorDetected);

      // Tambahkan class ke body untuk styling khusus
      if (capacitorDetected) {
        document.body.classList.add('capacitor-android');
      } else {
        document.body.classList.add('web-browser');
      }
    };

    detectPlatform();

    // Cleanup saat komponen unmount
    return () => {
      document.body.classList.remove('capacitor-android', 'web-browser');
    };
  }, []);
  // Data layanan berdasarkan gambar referensi
  const layananData: LayananItem[] = [
    {
      id: 'ac-service',
      title: 'Air Conditioner Service',
      image: '/images/layanan/ac-service.jpg',
      description: 'General Service & Maintenance Auto Air Conditioner System',
      features: [
        'Alat support & service r/v, bus, dll',
        'Alat berat produksi Excavator, Dozer',
        'Alat berat produksi Crane',
        'Service peralatan pendukung pertambangan'
      ]
    },
    {
      id: 'ban-velg',
      title: 'Ban & Velg (Tyre)',
      image: '/images/layanan/ban-velg.jpg',
      description: 'Support Repair & Rental Tyre',
      features: [
        'Penyediaan ban baru - bekas berkualitas',
        'Service ban bocor, ganti ban baru',
        'Penyediaan ban untuk berbagai jenis kendaraan',
        'Berbagai ukuran ban sesuai kebutuhan',
        'Solusi perawatan ban untuk heavy equipment',
        'Rental ban untuk proyek jangka pendek',
        'Analisa'
      ]
    },
    {
      id: 'fabrikasi-welding',
      title: 'Fabrikasi & Welding',
      image: '/images/layanan/fabrikasi-welding.jpg',
      description: 'Welding, Repair & Fabrikasi Professional Peralatan Industri',
      features: [
        'Modifikasi alat berat sesuai kebutuhan',
        'Repair cepat, tools, unit attachment',
        'Fabrikasi komponen khusus',
        'Fabrikasi komponen khusus',
        'Pengelasan & finishing berkualitas'
      ]
    }
  ];

  return (
    <div className={`layanan-container ${isCapacitor ? 'capacitor-mode' : 'web-mode'}`}>
      {/* Universal Header */}
      <UniversalHeader />

      {/* Main Content */}
      <main className="layanan-main">
        {/* Page Title */}
        <div className="layanan-title-section">
          <h2 className="layanan-title">LAYANAN</h2>
        </div>

        {/* Services Grid */}
        <div className="layanan-grid">
          {layananData.map((layanan) => (
            <div key={layanan.id} className="layanan-card">
              {/* Service Image */}
              <div className="layanan-image-container">
                <img
                  src={layanan.image}
                  alt={layanan.title}
                  className="layanan-image"
                  onError={(e) => {
                    // Fallback image if service image not found
                    (e.target as HTMLImageElement).src = '/images/icons/icon-192x192.png';
                  }}
                />
              </div>

              {/* Service Content */}
              <div className="layanan-content">
                {/* Service Icon and Title */}
                <div className="layanan-header-section">
                  <div className="layanan-icon">
                    {layanan.id === 'ac-service' && '❄️'}
                    {layanan.id === 'ban-velg' && '🚗'}
                    {layanan.id === 'fabrikasi-welding' && '🔧'}
                  </div>
                  <h3 className="layanan-card-title">{layanan.title}</h3>
                </div>

                {/* Service Description */}
                <p className="layanan-description">{layanan.description}</p>

                {/* Service Features */}
                <ul className="layanan-features">
                  {layanan.features.map((feature, index) => (
                    <li key={index} className="layanan-feature-item">
                      <span className="layanan-feature-icon">✓</span>
                      <span className="layanan-feature-text">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

      </main>

      {/* Universal Home Button */}
      <HomeButton />
    </div>
  );
};

export default Layanan;
