/* App.css - Styling utama untuk aplikasi PPWA */

/* Container utama aplikasi */
.app-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
}

/* Main content area - No scroll layout */
main {
  flex: 1;
  width: 100%;
  overflow: hidden;
}

/* Consistent layout for resolutions above 1000px (tablets and desktops) */
@media screen and (min-width: 1000px) {
  .app-container {
    height: 100vh;
    max-height: 100vh;
  }

  /* Ensure no-scroll layout for larger screens */
  main {
    padding: 0;
    overflow: hidden;
  }
}

/* Responsive adjustments untuk tablet portrait */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .app-container {
    max-width: 100%;
  }
}

/* Responsive adjustments untuk mobile */
@media screen and (max-width: 767px) {
  .app-container {
    max-width: 100%;
  }

  main {
    padding: 0;
  }
}

/* Utility classes untuk responsive design */
.container-responsive {
  width: 100%;
  max-width: 1366px;
  margin: 0 auto;
  padding: 0 20px;
}

@media screen and (max-width: 767px) {
  .container-responsive {
    padding: 0 15px;
  }
}

/* Smooth transitions untuk semua elemen */
* {
  transition: all 0.3s ease;
}

/* Optimasi untuk touch devices */
@media (hover: none) and (pointer: coarse) {
  button, .btn, .tab {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Landscape orientation optimizations */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .app-container {
    height: 100vh;
  }

  main {
    overflow-y: auto;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .app-container {
    border: 2px solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* Print styles */
@media print {
  .app-container {
    height: auto;
    overflow: visible;
  }

  main {
    overflow: visible;
  }
}