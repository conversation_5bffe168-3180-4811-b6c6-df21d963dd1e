/* Layanan Component Styles - Responsive untuk semua ukuran layar */
/* Critical: Identical layout for web and Capacitor PWA at all resolutions */

/* Main container - No scroll layout with precise viewport control */
.layanan-container {
  height: 100vh;
  max-height: 100vh;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg,
    oklch(var(--p) / 0.15) 0%,
    oklch(var(--s) / 0.1) 50%,
    oklch(var(--a) / 0.05) 100%);
  color: oklch(var(--bc));
  font-family: 'Poppins', sans-serif;
  /* Android/Web font consistency - Critical for PWA */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
  /* Precise layout control */
  display: flex;
  flex-direction: column;
  position: relative;
  /* Prevent any content overflow */
  box-sizing: border-box;
}

/* Main content area - Precise height control for no-scroll layout */
.layanan-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Critical: Ensure content fits within viewport */
  min-height: 0;
  box-sizing: border-box;
  /* Prevent content from exceeding container bounds */
  contain: layout style;
}

/* Title section - Fixed height for consistent layout */
.layanan-title-section {
  text-align: left;
  margin-bottom: 1rem;
  padding-left: 1rem;
  flex-shrink: 0;
  /* Fixed height to prevent layout shifts */
  height: auto;
  min-height: 3rem;
  display: flex;
  align-items: center;
}

.layanan-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: oklch(var(--p));
  letter-spacing: 0.05em;
  margin: 0;
  /* Prevent text overflow */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Services grid - Precise height control for no-scroll */
.layanan-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  flex: 1;
  overflow: hidden;
  align-content: start;
  /* Critical: Ensure grid fits within available space */
  min-height: 0;
  max-height: 100%;
  box-sizing: border-box;
  /* Prevent grid items from overflowing */
  contain: layout;
}

/* Service card - Optimized for consistent rendering */
.layanan-card {
  background: oklch(var(--b1));
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 25px -5px oklch(var(--p) / 0.1),
              0 8px 10px -6px oklch(var(--p) / 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid oklch(var(--b3));
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
  /* Critical: Ensure consistent card sizing */
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  box-sizing: border-box;
  /* Prevent content overflow */
  contain: layout style;
}

.layanan-card:nth-child(1) { animation-delay: 0.1s; }
.layanan-card:nth-child(2) { animation-delay: 0.2s; }
.layanan-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.layanan-card:hover,
.layanan-card:focus {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px -10px oklch(var(--p) / 0.15),
              0 16px 20px -12px oklch(var(--p) / 0.15);
  outline: 2px solid oklch(var(--p));
  outline-offset: 2px;
}

.layanan-card:focus {
  outline: 2px solid oklch(var(--p));
  outline-offset: 2px;
}

/* Service image - Optimized height for no-scroll layout */
.layanan-image-container {
  width: 100%;
  height: 120px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  /* Ensure consistent image rendering across platforms */
  background-color: oklch(var(--b2));
}

.layanan-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  /* Prevent image rendering issues on Android */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.layanan-card:hover .layanan-image {
  transform: scale(1.05);
}

/* Service content - Optimized padding for no-scroll layout */
.layanan-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  /* Critical: Ensure content fits within card bounds */
  min-height: 0;
  overflow: hidden;
  box-sizing: border-box;
}

/* Service header section */
.layanan-header-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.layanan-icon {
  font-size: 1.5rem;
  background: oklch(var(--p) / 0.1);
  padding: 0.5rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 3rem;
  min-height: 3rem;
}

.layanan-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: oklch(var(--p));
  margin: 0;
  line-height: 1.3;
}

/* Service description */
.layanan-description {
  font-size: 0.85rem;
  color: oklch(var(--bc) / 0.8);
  margin-bottom: 0.75rem;
  line-height: 1.4;
  font-weight: 500;
}

/* Service features - Compact layout with precise overflow control */
.layanan-features {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow: hidden;
  /* Ensure features list doesn't exceed available space */
  min-height: 0;
  max-height: 100%;
  display: flex;
  flex-direction: column;
}

.layanan-feature-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  line-height: 1.3;
}

.layanan-feature-item:last-child {
  margin-bottom: 0;
}

.layanan-feature-icon {
  color: oklch(var(--s));
  font-weight: 600;
  font-size: 0.8rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.layanan-feature-text {
  color: oklch(var(--bc) / 0.7);
  flex: 1;
}



/* CRITICAL RESPONSIVE DESIGN RULES */
/* These rules ensure IDENTICAL layout for web and Capacitor PWA */

/* CRITICAL: Consistent layout for resolutions >= 1000px width × 600px height */
/* This applies to tablets and desktops - MUST be identical in web and Capacitor */
@media screen and (min-width: 1000px) and (min-height: 600px) {
  .layanan-container {
    height: 100vh;
    max-height: 100vh;
    min-height: 100vh;
    /* Force exact viewport dimensions */
    width: 100vw;
    max-width: 100vw;
  }

  .layanan-main {
    /* Precise content area sizing */
    max-width: 1200px;
    padding: 0 2rem;
  }

  .layanan-title-section {
    min-height: 4rem;
    margin-bottom: 1.5rem;
  }

  .layanan-title {
    font-size: 2rem;
    /* Ensure consistent font rendering */
    font-weight: 700;
    line-height: 1.2;
  }

  .layanan-grid {
    /* Fixed 3-column layout for consistency */
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    /* Ensure grid fills available space precisely */
    height: auto;
    max-height: calc(100vh - 8rem);
  }

  .layanan-card {
    /* Consistent card dimensions */
    min-height: 400px;
    max-height: 450px;
  }

  .layanan-image-container {
    height: 140px;
    /* Consistent image sizing */
    flex-shrink: 0;
  }

  .layanan-content {
    padding: 1.25rem;
    /* Ensure content area is properly sized */
    flex: 1;
    min-height: 0;
  }

  .layanan-description {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
  }

  .layanan-feature-item {
    font-size: 0.85rem;
    margin-bottom: 0.6rem;
    line-height: 1.3;
  }
}

/* CRITICAL: Tablet Portrait and smaller screens (below 1000px width) */
@media screen and (max-width: 999px) {
  .layanan-container {
    height: 100vh;
    max-height: 100vh;
    min-height: 100vh;
    /* Maintain no-scroll layout */
    overflow: hidden;
  }

  .layanan-main {
    padding: 0 1rem;
  }

  .layanan-title-section {
    min-height: 3rem;
    margin-bottom: 1rem;
  }

  .layanan-title {
    font-size: 1.5rem;
    font-weight: 700;
  }

  .layanan-grid {
    /* Responsive grid for smaller screens */
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    max-height: calc(100vh - 6rem);
  }

  .layanan-card {
    min-height: 350px;
    max-height: 400px;
  }

  .layanan-image-container {
    height: 120px;
  }

  .layanan-content {
    padding: 1rem;
  }

  .layanan-description {
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
  }

  .layanan-feature-item {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }
}

/* CRITICAL: Mobile screens (max-width: 767px) */
@media screen and (max-width: 767px) {
  .layanan-container {
    height: 100vh;
    max-height: 100vh;
    min-height: 100vh;
    /* Strict no-scroll layout for mobile */
    overflow: hidden;
  }

  .layanan-main {
    padding: 0 0.75rem;
  }

  .layanan-title-section {
    min-height: 2.5rem;
    margin-bottom: 0.75rem;
  }

  .layanan-title {
    font-size: 1.25rem;
    font-weight: 700;
  }

  .layanan-grid {
    /* Single column layout for mobile */
    grid-template-columns: 1fr;
    gap: 0.75rem;
    max-height: calc(100vh - 5rem);
  }

  .layanan-card {
    border-radius: 0.75rem;
    min-height: 300px;
    max-height: 350px;
  }

  .layanan-image-container {
    height: 100px;
  }

  .layanan-content {
    padding: 0.75rem;
  }

  .layanan-card-title {
    font-size: 1rem;
  }

  .layanan-description {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }

  .layanan-feature-item {
    font-size: 0.75rem;
    margin-bottom: 0.4rem;
  }
}

/* CRITICAL PWA AND CAPACITOR OPTIMIZATIONS */
/* These rules ensure identical rendering between web and Android app */

/* PWA-specific optimizations */
@media screen and (display-mode: standalone) {
  .layanan-container {
    /* Ensure proper viewport handling in PWA mode */
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
    max-height: 100vh;
    max-height: 100dvh;
  }
}

/* Capacitor Android app optimizations */
.capacitor-android .layanan-container {
  /* Force consistent viewport dimensions */
  height: 100vh !important;
  max-height: 100vh !important;
  min-height: 100vh !important;
  /* Prevent any scrolling issues */
  overflow: hidden !important;
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .layanan-image {
    /* Optimize image rendering for high DPI displays */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  .layanan-card {
    /* Ensure crisp borders on high DPI */
    border-width: 0.5px;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .layanan-card {
    /* Optimize touch interactions */
    min-height: 44px;
    cursor: pointer;
  }

  .layanan-card:hover {
    /* Disable hover effects on touch devices */
    transform: none;
  }

  .layanan-card:active {
    /* Add touch feedback */
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Landscape orientation handling */
@media screen and (orientation: landscape) and (max-height: 600px) {
  .layanan-container {
    /* Adjust for landscape orientation */
    height: 100vh;
    max-height: 100vh;
  }

  .layanan-grid {
    /* Optimize grid for landscape */
    max-height: calc(100vh - 4rem);
  }

  .layanan-card {
    /* Reduce card height in landscape */
    min-height: 280px;
    max-height: 320px;
  }

  .layanan-image-container {
    height: 80px;
  }
}

/* Accessibility and reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .layanan-card {
    animation: none;
    transition: none;
  }

  .layanan-image {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .layanan-card {
    border: 2px solid oklch(var(--bc));
  }

  .layanan-title {
    color: oklch(var(--bc));
  }
}
