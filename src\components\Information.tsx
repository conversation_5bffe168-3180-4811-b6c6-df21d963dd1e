import React, { useState } from 'react';
import './Information.css';
import type { InformationItem, InformationProps } from '../types';
import UniversalHeader from './UniversalHeader';
import HomeButton from './HomeButton';

/**
 * Komponen Information - Menampilkan informasi seputar PWB
 * Layout split dengan daftar informasi di kiri dan detail di kanan
 * Fitur reordering: item yang diklik pindah ke posisi teratas
 */
const Information: React.FC<InformationProps> = ({ className = '' }) => {
  // Data informasi berdasarkan gambar referensi
  const initialInformationData: InformationItem[] = [
    {
      id: 'sumpah-pemuda',
      title: 'Selamat Hari SUMPAH PEMUDA',
      image: '/images/information/sumpah-pemuda.jpg',
      description: 'Ucapan Selamat Hari Pancasila',
      content: `Selamat Hari Sumpah Pemuda! 

Pada tanggal 28 Oktober 1928, para pemuda Indonesia berkumpul dalam Kongres Pemuda II dan mengikrarkan Sumpah Pemuda yang berisi tekad untuk berbangsa satu, bertanah air satu, dan berbahasa satu yaitu Indonesia.

Semangat persatuan dan kesatuan yang diwariskan oleh para pemuda 1928 ini menjadi fondasi kuat bagi bangsa Indonesia dalam meraih kemerdekaan dan membangun negara.

Mari kita teruskan semangat Sumpah Pemuda dalam kehidupan sehari-hari, dengan menjaga persatuan dan kesatuan bangsa, serta berkontribusi positif bagi kemajuan Indonesia.`,
      date: '28 Oktober 2024',
      category: 'Peringatan Nasional'
    },
    {
      id: 'diskon-ramadhan',
      title: 'Tentapat Diskon gede bulan Ramadhan',
      image: '/images/information/diskon-ramadhan.jpg',
      description: 'Promo spesial bulan suci Ramadhan',
      content: `Selamat datang bulan suci Ramadhan! 

PT Putera Wibowo Borneo memberikan promo spesial untuk seluruh layanan kami selama bulan Ramadhan:

• Diskon 15% untuk layanan Air Conditioner Service
• Diskon 20% untuk layanan Ban & Velg (Tyre)
• Diskon 10% untuk layanan Fabrikasi & Welding
• Paket bundling dengan harga khusus
• Gratis konsultasi teknis

Promo berlaku mulai 1 Ramadhan hingga Idul Fitri. Syarat dan ketentuan berlaku.

Hubungi tim kami untuk informasi lebih lanjut dan dapatkan penawaran terbaik untuk kebutuhan alat berat Anda.`,
      date: '15 Maret 2024',
      category: 'Promo & Diskon'
    },
    {
      id: 'sertifikat-iso',
      title: 'Prestasi Penghargaan Sertifikat ISO Terbaru',
      image: '/images/information/sertifikat-iso.jpg',
      description: 'Pencapaian standar internasional',
      content: `Dengan bangga kami umumkan bahwa PT Putera Wibowo Borneo telah meraih sertifikat ISO 9001:2015 untuk Sistem Manajemen Mutu.

Pencapaian ini merupakan bukti komitmen kami dalam:
• Memberikan layanan berkualitas tinggi
• Menerapkan standar internasional
• Meningkatkan kepuasan pelanggan
• Melakukan perbaikan berkelanjutan

Sertifikat ini diperoleh setelah melalui audit ketat dari lembaga sertifikasi internasional yang diakui. Hal ini memperkuat posisi kami sebagai mitra terpercaya dalam industri alat berat dan layanan teknologi industri.

Terima kasih atas kepercayaan seluruh mitra dan pelanggan yang telah mendukung perjalanan kami.`,
      date: '10 Februari 2024',
      category: 'Prestasi & Penghargaan'
    },
    {
      id: 'tips-safety',
      title: 'Tips Safety dalam lingkungan Pertambangan untuk keselamatan bersama',
      image: '/images/information/tips-safety.jpg',
      description: 'Panduan keselamatan kerja di area pertambangan',
      content: `Keselamatan di lingkungan pertambangan adalah prioritas utama. Berikut tips penting untuk menjaga keselamatan:

**Persiapan Sebelum Bekerja:**
• Gunakan APD (Alat Pelindung Diri) lengkap
• Periksa kondisi alat dan mesin sebelum digunakan
• Pastikan area kerja aman dan bebas dari bahaya

**Selama Bekerja:**
• Patuhi semua prosedur keselamatan yang berlaku
• Komunikasi yang jelas dengan tim
• Waspada terhadap kondisi lingkungan sekitar
• Laporkan segera jika ada kondisi tidak aman

**Setelah Bekerja:**
• Matikan semua mesin dan peralatan
• Bersihkan dan simpan APD dengan benar
• Laporkan kondisi alat yang memerlukan perbaikan

Ingat: Keselamatan adalah tanggung jawab bersama. Satu kelalaian dapat membahayakan banyak orang.`,
      date: '5 Januari 2024',
      category: 'Tips & Panduan'
    },
    {
      id: 'jangan-lewatkan-diskon',
      title: 'Jangan Lewatkan Diskon dibawah Ramadhan',
      image: '/images/information/jangan-lewatkan-diskon.jpg',
      description: 'Promo terbatas waktu',
      content: `Jangan sampai terlewat! Promo spesial Ramadhan dari PT Putera Wibowo Borneo:

**Penawaran Terbatas:**
• Berlaku hingga akhir bulan Ramadhan
• Kuota terbatas untuk setiap layanan
• First come, first served

**Layanan yang Tersedia:**
• Service AC kendaraan berat - Diskon 15%
• Perbaikan dan rental ban - Diskon 20%
• Fabrikasi dan welding - Diskon 10%

**Cara Mendapatkan Diskon:**
1. Hubungi customer service kami
2. Sebutkan kode promo "RAMADHAN2024"
3. Jadwalkan layanan sesuai kebutuhan
4. Nikmati layanan berkualitas dengan harga spesial

Hubungi kami sekarang juga di nomor yang tertera atau kunjungi kantor kami langsung.`,
      date: '20 Maret 2024',
      category: 'Promo & Diskon'
    }
  ];

  const [informationList, setInformationList] = useState<InformationItem[]>(initialInformationData);
  const [selectedItem, setSelectedItem] = useState<InformationItem | null>(null);

  /**
   * Handle item click - reorder list and show detail
   */
  const handleItemClick = (item: InformationItem) => {
    // Set selected item for detail view
    setSelectedItem(item);

    // Reorder list: move clicked item to top
    const newList = [item, ...informationList.filter(info => info.id !== item.id)];
    setInformationList(newList);
  };

  return (
    <div className={`information-container ${className}`}>
      {/* Universal Header */}
      <UniversalHeader />

      {/* Main Content */}
      <main className="information-main">
        <div className="information-layout">
          {/* Left Section - Information List */}
          <section className="information-list-section" aria-label="Daftar informasi">
            <div className="information-list" role="list">
              {informationList.map((item) => (
                <article
                  key={item.id}
                  className={`information-item ${selectedItem?.id === item.id ? 'active' : ''}`}
                  onClick={() => handleItemClick(item)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleItemClick(item);
                    }
                  }}
                  role="listitem"
                  tabIndex={0}
                  aria-label={`Informasi: ${item.title}`}
                  aria-selected={selectedItem?.id === item.id}
                >
                  <div className="information-item-image">
                    <img
                      src={item.image}
                      alt={item.title}
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/images/icons/icon-192x192.png';
                      }}
                    />
                  </div>
                  <div className="information-item-content">
                    <h3 className="information-item-title">{item.title}</h3>
                    <p className="information-item-description">{item.description}</p>
                    <div className="information-item-meta">
                      <span className="information-item-date">{item.date}</span>
                      <span className="information-item-category">{item.category}</span>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </section>

          {/* Right Section - Detail View */}
          <section className="information-detail-section" aria-label="Detail informasi">
            {selectedItem ? (
              <div className="information-detail">
                <div className="information-detail-header">
                  <img
                    src={selectedItem.image}
                    alt={selectedItem.title}
                    className="information-detail-image"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/images/icons/icon-192x192.png';
                    }}
                  />
                  <div className="information-detail-meta">
                    <h2 className="information-detail-title">{selectedItem.title}</h2>
                    <div className="information-detail-info">
                      <span className="information-detail-date">{selectedItem.date}</span>
                      <span className="information-detail-category">{selectedItem.category}</span>
                    </div>
                  </div>
                </div>
                <div className="information-detail-content">
                  {selectedItem.content.split('\n').map((paragraph, index) => (
                    <p key={index} className="information-detail-paragraph">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>
            ) : (
              <div className="information-detail-placeholder">
                <div className="placeholder-content">
                  <div className="placeholder-icon">📰</div>
                  <h3 className="placeholder-title">Pilih Informasi</h3>
                  <p className="placeholder-text">
                    Klik salah satu item informasi di sebelah kiri untuk melihat detail lengkapnya
                  </p>
                </div>
              </div>
            )}
          </section>
        </div>

      </main>

      {/* Universal Home Button */}
      <HomeButton />
    </div>
  );
};

export default Information;
